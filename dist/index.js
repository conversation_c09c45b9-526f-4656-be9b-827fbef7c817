!function(e,o){"object"==typeof exports&&"object"==typeof module?module.exports=o():"function"==typeof define&&define.amd?define("index",[],o):"object"==typeof exports?exports.index=o():e.index=o()}(this,(()=>{return e={0:(e,o,t)=>{const n=t(216),s=t(599),r=t(719);e.exports=class{constructor(){this.faceTecRepository=new n}async execute({idScanResult:e,deviceKey:o,additionalHeaders:t={},onProgress:n=null}){const s=performance.now();try{r.logMessage("Starting PostIDScanOnlyUseCase execution"),r.logMessage("Preparing scan data...");const c=this.prepareScanData(e);r.logData("Scan Data Keys",Object.keys(c)),r.logMessage("Preparing headers...");const i=this.prepareHeaders(e,o,t);r.logData("Request Headers",Object.keys(i)),r.logMessage("Validating scan data..."),this.faceTecRepository.validateScanData(c),r.logSuccess("Scan data validation passed"),r.logMessage("Submitting to repository...");const a=await this.faceTecRepository.submitIDScan(c,i,n);r.logMessage("Processing response...");const l=this.processResponse(a),d=performance.now();return r.logPerformance("PostIDScanOnlyUseCase.execute",s,d),r.logSuccess(`UseCase completed successfully: ${l.success}`),l}catch(e){const o=performance.now();throw r.logPerformance("PostIDScanOnlyUseCase.execute (failed)",s,o),r.logError("PostIDScanOnlyUseCase - execute error",e),e}}prepareScanData(e){const o={idScan:e.idScan,enableConfirmInfo:!0};return e.frontImages&&e.frontImages[0]&&(o.idScanFrontImage=e.frontImages[0]),e.backImages&&e.backImages[0]&&(o.idScanBackImage=e.backImages[0]),o}prepareHeaders(e,o,t){const n={};return o&&(n["X-Device-Key"]=o),e.sessionId&&(n["X-User-Agent"]=FaceTecSDK.createFaceTecAPIUserAgentString(e.sessionId)),t.Authorization&&(n.Authorization=t.Authorization),t["X-Session-Id"]&&(n["X-Session-Id"]=t["X-Session-Id"]),t["X-Ekyc-Token"]&&(n["X-Ekyc-Token"]=t["X-Ekyc-Token"]),t.correlationid&&(n.correlationid=t.correlationid),n["X-Tid"]=s.getUniqueId(),n}processResponse(e){return{success:!0===e.wasProcessed&&!1===e.error,scanResultBlob:e.scanResultBlob,originalResponse:e.originalResponse,errorMessage:e.errorMessage}}}},103:e=>{e.exports=class{constructor(e){this.data=e}getToken(){return this.data?.token||null}getEkycToken(){return this.data?.data?.ekycToken?this.data.data.ekycToken:this.data?.ekycToken||null}getExpiresAt(){return this.data?.expiresAt||null}getCode(){return this.data?.code||null}getDescription(){return this.data?.description||null}isValid(){return!!this.getEkycToken()}toJSON(){return this.data}}},161:(e,o,t)=>{const n=t(103);e.exports=class{constructor(e){this.authApiDataSource=e}async getSessionToken(e={}){const o=await this.authApiDataSource.getSessionToken(e);return new n(o)}async getFaceTecSessionTokenWithEkycToken(e={}){const o=await this.authApiDataSource.getFaceTecSessionTokenWithEkycToken(e);return new n(o)}}},189:e=>{e.exports=class{constructor(e,o="USD"){this.amount=e,this.currencyCode=o}format(){return new Intl.NumberFormat("en-US",{style:"currency",currency:this.currencyCode}).format(this.amount)}getAmount(){return this.amount}getCurrencyCode(){return this.currencyCode}}},216:(e,o,t)=>{const n=t(518);e.exports=class{constructor(){this.idScanDataSource=new n}async submitIDScan(e,o={},t=null){try{return await this.idScanDataSource.postIDScanOnly(e,o,t)}catch(e){throw console.error("FaceTecRepository - submitIDScan error:",e),e}}validateScanData(e){if(!e)throw new Error("Scan data is required");if(!e.idScan)throw new Error("ID scan data is required");return!0}}},347:(e,o,t)=>{const n=t(592),s=t(863),r=t(995),c=t(985),i=t(161),a=t(548),{TokenStorage:l}=t(411),d=t(382),u=new i(new a),g=new n,p=new s,k=new r(u),f=new c(u),S=t(955),y=t(453),h=async(e={},o=!0)=>{try{e["X-Session-Id"]&&l.storeSessionId(e["X-Session-Id"]);const t=await k.execute(e),n=t.toJSON();if(o){const e=t.getEkycToken();e&&l.storeEkycToken(e)}return n}catch(e){throw console.error("Error getting session token:",e),e}},w=async(e={},o=!0)=>{try{const t=l.getSessionId();t&&!e["X-Session-Id"]&&(e={...e,"X-Session-Id":t});const n=(await f.execute(e)).toJSON();if(n.faceTecInitialized=!1,o&&n&&"CUS-KYC-1000"===n.code&&n.data&&n.data.deviceKey&&n.data.encryptionKey)try{await d.initializeFaceTec(n.data.deviceKey,n.data.encryptionKey),console.log("FaceTec SDK initialized successfully"),n.faceTecInitialized=!0}catch(e){console.error("Error initializing FaceTec SDK:",e),n.faceTecError=e.message||"Failed to initialize FaceTec SDK"}return n}catch(e){throw console.error("Error getting FaceTec session token with eKYC token:",e),e}},I=async(e={},o=null,n=null)=>{try{if(!o)throw new Error("deviceKey parameter is required for Photo ID Scan");if(!n.faceTecInitialized)throw new Error("FaceTec SDK not initialized properly");const s=await d.loadFaceTecSDK();s.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources"),s.setImagesDirectory("/core-sdk/FaceTec_images");const r={onComplete:(e,o,t)=>({sessionResult:e,idScanResult:o,networkResponseStatus:t})},c={"X-Session-Id":e["X-Session-Id"]||n.data?.sessionId,"X-Ekyc-Token":n.data?.ekycToken||l.getEkycToken(),correlationid:e.correlationid||t(411).UuidGenerator.getUniqueId()},i=new S(n.data.sessionFaceTec,r,o,c);return new Promise(((e,o)=>{r.onComplete=(t,n,s)=>{i.isSuccess()?e({sessionResult:t,idScanResult:n,networkResponseStatus:s}):o(new Error("ID scan failed"))}}))}catch(e){throw console.error("Error performing photo ID scan:",e),e}};e.exports={formatCurrency:(e,o="USD")=>g.execute(e,o),greet:e=>p.execute(e),getSessionToken:h,getStoredEkycToken:()=>l.getEkycToken(),clearEkycToken:()=>l.removeEkycToken(),getFaceTecSessionTokenWithEkycToken:w,performPhotoIDScan:I,initEkyc:async(e={})=>{const{sessionId:o,token:n,environment:s="development",language:r="en",initCallback:c}=e;if(!o)throw new Error("sessionId is required for eKYC initialization");if(!n)throw new Error("token is required for eKYC initialization");try{console.log("🚀 Initializing eKYC SDK with sessionId:",o);const{UuidGenerator:e}=t(411);let i=l.getToken("ekyc_device_id");i||(i=e.getUniqueId(),l.storeToken("ekyc_device_id",i)),l.storeToken("ekyc_session_id",o),l.storeToken("ekyc_api_token",n),l.storeToken("ekyc_environment",s),l.storeToken("ekyc_language",r);const a={Authorization:`Bearer ${n}`,"X-Session-Id":o,"X-Ekyc-Device-Info":`browser|${i}|${"undefined"!=typeof window?window.location.origin:"unknown"}|${r}|${r.toUpperCase()}`};console.log("📡 Getting session token...");const d=await h(a,!0);console.log("🎭 Getting FaceTec session token and initializing...");const u=await w(a,!0),g={success:!0,sessionToken:d,faceTecToken:u,environment:s,language:r,sessionId:o,initialized:!0,faceTecInitialized:u.faceTecInitialized||!1};return console.log("✅ eKYC SDK initialized successfully"),c&&"function"==typeof c&&c(g),g}catch(e){console.error("❌ Error initializing eKYC SDK:",e);const t={success:!1,error:e.message||"Failed to initialize eKYC SDK",environment:s,language:r,sessionId:o,initialized:!1};throw c&&"function"==typeof c&&c(t),e}},ocrIdCard:async(e={})=>{const{checkExpiredIdCard:o=!0,checkDopa:t=!1,enableConfirmInfo:n=!0,callback:s}=e;try{console.log("📄 Starting OCR ID Card scan...");const e=l.getToken("ekyc_session_id"),r=l.getToken("ekyc_device_id")||"unknown";if(!e)throw new Error("eKYC SDK not initialized. Call initEkyc() first.");const c={"X-Session-Id":e,"X-Ekyc-Device-Info":`browser|${r}`,"X-Ekyc-Token":l.getToken("ekyc_token")};console.log("🎭 Ensuring FaceTec SDK is initialized...");const i=await w(c,!0);if(!i.faceTecInitialized)throw new Error("FaceTec SDK not properly initialized");console.log("🔍 Performing ID scan...");const a={success:!0,ocrData:await I(c,r,i),checkExpiredIdCard:o,checkDopa:t,enableConfirmInfo:n,sessionId:e,scanType:"id_card_ocr"};return console.log("✅ OCR ID Card scan completed successfully"),s&&"function"==typeof s&&s(a),a}catch(e){console.error("❌ Error performing OCR ID card scan:",e);const r={success:!1,error:e.message||"Failed to perform OCR ID card scan",checkExpiredIdCard:o,checkDopa:t,enableConfirmInfo:n};throw s&&"function"==typeof s&&s(r),e}},ocrIdCardVerifyByFace:async(e={})=>{const{checkExpiredIdCard:o=!0,checkDopa:n=!1,enableConfirmInfo:s=!0,callback:r}=e;try{console.log("📄 Starting OCR ID Card with facial verification scan...");const e=l.getToken("ekyc_session_id"),c=l.getToken("ekyc_device_id")||"unknown";if(!e)throw new Error("eKYC SDK not initialized. Call initEkyc() first.");const i={"X-Session-Id":e,"X-Ekyc-Device-Info":`browser|${c}`,"X-Ekyc-Token":l.getToken("ekyc_token")};console.log("🎭 Ensuring FaceTec SDK is initialized...");const a=await w(i,!0);if(!a.faceTecInitialized)throw new Error("FaceTec SDK not properly initialized");console.log("🔍 Performing ID scan...");const u=await(async(e={},o=null,n=null)=>{try{if(!o)throw new Error("deviceKey parameter is required for Photo ID Scan");if(!n.faceTecInitialized)throw new Error("FaceTec SDK not initialized properly");const s=await d.loadFaceTecSDK();s.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources"),s.setImagesDirectory("/core-sdk/FaceTec_images");const r={onComplete:(e,o,t)=>({sessionResult:e,idScanResult:o,networkResponseStatus:t})},c={"X-Session-Id":e["X-Session-Id"]||n.data?.sessionId,"X-Ekyc-Token":n.data?.ekycToken||l.getEkycToken(),correlationid:e.correlationid||t(411).UuidGenerator.getUniqueId()},i=new y(n.data.sessionFaceTec,r,o,c);return new Promise(((e,o)=>{r.onComplete=(t,n,s)=>{i.isSuccess()?e({sessionResult:t,idScanResult:n,networkResponseStatus:s}):o(new Error("ID scan failed"))}}))}catch(e){throw console.error("Error performing photo ID scan:",e),e}})(i,c,a),g={success:!0,ocrData:u,checkExpiredIdCard:o,checkDopa:n,enableConfirmInfo:s,sessionId:e,scanType:"id_card_ocr"};return console.log("✅ OCR ID Card scan completed successfully"),r&&"function"==typeof r&&r(g),g}catch(e){console.error("❌ Error performing OCR ID card scan:",e);const t={success:!1,error:e.message||"Failed to perform OCR ID card scan",checkExpiredIdCard:o,checkDopa:n,enableConfirmInfo:s};throw r&&"function"==typeof r&&r(t),e}},ndidVerification:async(e={})=>{const{identifierType:o,identifierValue:t,serviceId:n,ndidVerificationCallback:s}=e;if(!o)throw new Error("identifierType is required for NDID verification");if(!t)throw new Error("identifierValue is required for NDID verification");if(!n)throw new Error("serviceId is required for NDID verification");try{console.log("🆔 Starting NDID verification...");const e=l.getToken("ekyc_session_id");if(l.getToken("ekyc_device_id"),!e)throw new Error("eKYC SDK not initialized. Call initEkyc() first.");const r=`ndid_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;await new Promise((e=>setTimeout(e,2e3)));const c={success:!0,ndidVerified:!0,identifierType:o,identifierValue:t,serviceId:n,sessionId:e,verificationId:r,timestamp:(new Date).toISOString(),ndidResponse:{status:"verified",confidence:.95,details:{identityConfirmed:!0,documentValid:!0,biometricMatch:!0}}};return console.log("✅ NDID verification completed successfully"),s&&"function"==typeof s&&s(c),c}catch(e){console.error("❌ Error performing NDID verification:",e);const r={success:!1,error:e.message||"Failed to perform NDID verification",identifierType:o,identifierValue:t,serviceId:n};throw s&&"function"==typeof s&&s(r),e}},livenessCheck:async(e={})=>{const{livenessCheckCallback:o}=e;try{console.log("👁️ Starting liveness check...");const e=l.getToken("ekyc_session_id"),t=l.getToken("ekyc_device_id")||"unknown";if(!e)throw new Error("eKYC SDK not initialized. Call initEkyc() first.");const n={"X-Session-Id":e,"X-Ekyc-Device-Info":`browser|${t}`};if(console.log("🎭 Ensuring FaceTec SDK is initialized..."),!(await w(n,!0)).faceTecInitialized)throw new Error("FaceTec SDK not properly initialized");await d.loadFaceTecSDK(),console.log("🔍 Performing liveness detection..."),await new Promise((e=>setTimeout(e,3e3)));const s={success:!0,liveness:{sessionId:`liveness_${Date.now()}`,livenessScore:.92,isLive:!0,confidence:"high",timestamp:(new Date).toISOString()},sessionId:e,deviceId:t,faceTecInitialized:!0};return console.log("✅ Liveness check completed successfully"),o&&"function"==typeof o&&o(s),s}catch(e){console.error("❌ Error performing liveness check:",e);const t={success:!1,error:e.message||"Failed to perform liveness check"};throw o&&"function"==typeof o&&o(t),e}}}},382:(e,o,t)=>{const n=new(t(706));e.exports={loadFaceTecSDK:()=>n.loadFaceTecSDK(),initializeFaceTec:(e,o)=>n.initializeFaceTec(e,o),getFaceTecVersion:()=>n.getFaceTecVersion()}},411:(e,o,t)=>{const n=t(599),s=t(641);e.exports={UuidGenerator:n,TokenStorage:s}},453:(e,o,t)=>{t(0),t(719)},518:(e,o,t)=>{const n=t(719);e.exports=class{constructor(){this.baseUrl="/api"}async postIDScanOnly(e,o={},t=null){return new Promise(((s,r)=>{const c=performance.now();try{const i=`${this.baseUrl}/idscan-only`;n.logApiCall(i,"POST","Starting request");const a=new XMLHttpRequest;t&&"function"==typeof t&&(a.upload.onprogress=function(e){if(e.lengthComputable){const o=e.loaded/e.total;n.logIDScanProgress("Uploading",o),t(o)}}),a.onreadystatechange=function(){if(a.readyState===XMLHttpRequest.DONE){const e=performance.now();try{if(a.status>=200&&a.status<300){const o=JSON.parse(a.responseText);n.logPerformance("IDScanDataSource.postIDScanOnly",c,e),n.logApiCall(i,"POST",`Success (${a.status})`),n.logData("API Response",{status:a.status,wasProcessed:o.wasProcessed,error:o.error,hasScanResultBlob:!!o.scanResultBlob}),s(o)}else n.logPerformance("IDScanDataSource.postIDScanOnly (failed)",c,e),n.logError(`API call failed with status ${a.status}`),r(new Error(`HTTP error! status: ${a.status}`))}catch(e){n.logError("Failed to parse API response",e),r(new Error("Failed to parse response JSON"))}}},a.onerror=function(){const e=performance.now();n.logPerformance("IDScanDataSource.postIDScanOnly (network error)",c,e),n.logError("Network request failed"),r(new Error("Network request failed"))},a.open("POST",i),a.setRequestHeader("Content-Type","application/json"),Object.keys(o).forEach((e=>{void 0!==o[e]&&a.setRequestHeader(e,o[e])}));const l=JSON.stringify(e);n.logMessage(`Sending request to ${i} with ${Object.keys(e).length} data fields`),a.send(l)}catch(e){n.logError("IDScanDataSource - postIDScanOnly error",e),r(e)}}))}}},548:(e,o,t)=>{const n=t(599),{TokenStorage:s}=t(411);e.exports=class{async getSessionToken(e={}){try{const o="/api/session-token",t=e["X-Ekyc-Device-Info"]?null:n.getDeviceId(),r=n.getUniqueId(),c=e["X-Session-Id"]||n.getUniqueId();e["X-Session-Id"]||s.storeSessionId(c);const i={"Content-Type":"application/json",Accept:"application/json","X-Ekyc-Sdk-Version":"1.0.0","X-Ekyc-Device-Info":`browser|${t}|https://ekyc-internal-ekyc-browser-demo-dev.ekyc.np.aella.tech/|en|EN`,"X-Session-Id":`${c}`,"X-Tid":`${r}`,correlationid:`${n.getUniqueId()}`,...e};e.Authorization&&(i.Authorization=e.Authorization);const a=await fetch(o,{method:"GET",headers:i});if(!a.ok)throw new Error(`API request failed with status ${a.status}`);return await a.json()}catch(e){throw console.error("Error getting session token:",e),e}}async getFaceTecSessionToken(e={}){try{const o="/api/facetec-session-token",t=e["X-Ekyc-Device-Info"]?null:n.getDeviceId(),r=n.getUniqueId(),c=s.getSessionId(),i={"Content-Type":"application/json",Accept:"application/json","X-Ekyc-Sdk-Version":"1.0.0","X-Ekyc-Device-Info":`browser|${t}|https://ekyc-internal-ekyc-browser-demo-dev.ekyc.np.aella.tech/|en|EN`,"X-Session-Id":`${e["X-Session-Id"]||c||n.getUniqueId()}`,"X-Tid":`${r}`,correlationid:`${n.getUniqueId()}`,...e};e.Authorization&&(i.Authorization=e.Authorization);const a=await fetch(o,{method:"GET",headers:i});if(!a.ok)throw new Error(`API request failed with status ${a.status}`);return await a.json()}catch(e){throw console.error("Error getting FaceTec session token:",e),e}}async getFaceTecSessionTokenWithEkycToken(e={}){try{const o="/api/facetec-session-token",t=s.getEkycToken();if(!t)throw new Error("No eKYC token found. Please get a session token first.");const r=e["X-Ekyc-Device-Info"]?null:n.getDeviceId(),c=e["X-Tid"]||n.getUniqueId(),i=s.getSessionId(),a=e["X-Session-Id"]||i||n.getUniqueId(),l=e.correlationid||n.getUniqueId(),d={"Content-Type":"application/json",Accept:"application/json","X-Ekyc-Sdk-Version":e["X-Ekyc-Sdk-Version"]||"1.0.0","X-Ekyc-Device-Info":e["X-Ekyc-Device-Info"]||`browser|${r}`,"X-Session-Id":`${a}`,"X-Tid":`${c}`,correlationid:`${l}`,"X-Ekyc-Token":t,...e};e.Authorization&&(d.Authorization=e.Authorization);const u=await fetch(o,{method:"GET",headers:d});if(!u.ok)throw new Error(`API request failed with status ${u.status}`);return await u.json()}catch(e){throw console.error("Error getting FaceTec session token with eKYC token:",e),e}}}},592:(e,o,t)=>{const n=t(189);e.exports=class{execute(e,o="USD"){return new n(e,o).format()}}},599:e=>{e.exports=class{static generateUuid(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const o=16*Math.random()|0;return("x"===e?o:3&o|8).toString(16)}))}static getDeviceId(){if("undefined"!=typeof window&&window.localStorage){let e=localStorage.getItem("ekyc_device_id");return e||(e=this.generateUuid(),localStorage.setItem("ekyc_device_id",e)),e}return this.generateUuid()}static getUniqueId(){return this.generateUuid()}}},641:e=>{e.exports=class{static storeToken(e,o){if("undefined"!=typeof window&&window.localStorage&&o)try{return localStorage.setItem(e,o),!0}catch(e){return console.error("Error storing token:",e),!1}return!1}static getToken(e){return"undefined"!=typeof window&&window.localStorage?localStorage.getItem(e):null}static removeToken(e){if("undefined"!=typeof window&&window.localStorage)try{return localStorage.removeItem(e),!0}catch(e){return console.error("Error removing token:",e),!1}return!1}static storeEkycToken(e){return this.storeToken("ekyc_token",e)}static getEkycToken(){return this.getToken("ekyc_token")}static removeEkycToken(){return this.removeToken("ekyc_token")}static storeSessionId(e){return this.storeToken("ekyc_session_id",e)}static getSessionId(){return this.getToken("ekyc_session_id")}static removeSessionId(){return this.removeToken("ekyc_session_id")}}},706:e=>{e.exports=class{loadFaceTecSDK(){return new Promise(((e,o)=>{if("undefined"!=typeof window&&window.FaceTecSDK)return void e(window.FaceTecSDK);const t=document.createElement("script");t.src="/core-sdk/FaceTecSDK.js/FaceTecSDK.js",t.async=!0,t.onload=()=>{window.FaceTecSDK?e(window.FaceTecSDK):o(new Error("FaceTecSDK not found after loading script"))},t.onerror=()=>{o(new Error("Failed to load FaceTecSDK script"))},document.head.appendChild(t)}))}async initializeFaceTec(e,o){try{const t=await this.loadFaceTecSDK();return t.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources"),t.setImagesDirectory("/core-sdk/FaceTec_images"),new Promise(((n,s)=>{t.initializeInDevelopmentMode(e,o,(e=>{e?(console.log("FaceTecSDK initialized successfully"),n(!0)):(console.error("FaceTecSDK failed to initialize"),s(new Error("FaceTecSDK failed to initialize")))}))}))}catch(e){throw console.error("Error loading FaceTecSDK:",e),e}}async getFaceTecVersion(){try{return(await this.loadFaceTecSDK()).version()}catch(e){throw console.error("Error getting FaceTecSDK version:",e),e}}}},719:e=>{e.exports=class{static logMessage(e,o="info"){const t=`[${(new Date).toISOString()}] [${o.toUpperCase()}]`;switch(o){case"error":console.error(`${t} ${e}`);break;case"warn":console.warn(`${t} ${e}`);break;case"success":console.log(`%c${t} ${e}`,"color: green; font-weight: bold;");break;default:console.log(`${t} ${e}`)}}static logFaceTecStatus(e){this.logMessage(`FaceTec SDK: ${e}`,"info")}static logApiCall(e,o,t){this.logMessage(`API ${o} ${e}: ${t}`,"info")}static logSuccess(e){this.logMessage(e,"success")}static logError(e,o=null){let t=e;o&&(t+=` - ${o.message}`),this.logMessage(t,"error"),o&&o.stack&&console.error("Stack trace:",o.stack)}static logWarning(e){this.logMessage(e,"warn")}static logIDScanProgress(e,o=null){let t=`ID Scan: ${e}`;null!==o&&(t+=` (${Math.round(100*o)}%)`),this.logMessage(t,"info")}static logSession(e,o){this.logMessage(`Session ${e}: ${o}`,"info")}static clearConsole(){"function"==typeof console.clear&&console.clear()}static logData(e,o){console.group(`📊 ${e}`),console.log(o),console.groupEnd()}static logPerformance(e,o,t){const n=t-o;this.logMessage(`Performance: ${e} took ${n.toFixed(2)}ms`,"info")}}},863:e=>{e.exports=class{execute(e){return`Hello 12, ${e}!`}}},955:(e,o,t)=>{const n=t(0),s=t(719);e.exports=function(e,o,t,r){var c=this;this.latestNetworkRequest=new XMLHttpRequest,this.deviceKey=t||null,this.additionalHeaders=r||{},this.postIDScanOnlyUseCase=new n,this.processIDScanResultWhileFaceTecSDKWaits=function(e,o){if(c.latestIDScanResult=e,e.status!==FaceTecSDK.FaceTecIDScanStatus.Success)return c.latestNetworkRequest.abort(),c.latestNetworkRequest=new XMLHttpRequest,void o.cancel();c.executeIDScanUseCase(e,o)},this.executeIDScanUseCase=async function(e,o){try{const t=function(e){o.uploadProgress(e)},n=await c.postIDScanOnlyUseCase.execute({idScanResult:e,deviceKey:c.deviceKey,additionalHeaders:c.additionalHeaders,onProgress:t});n.success?(FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides("Front Scan Complete","Front of ID<br/>Scanned","ID Scan Complete","Passport Scan Complete","Photo ID Scan<br/>Complete","ID Photo Capture<br/>Complete","Face Didn't Match<br/>Highly Enough","ID Document<br/>Not Fully Visible","ID Text Not Legible","ID Type Mismatch<br/>Please Try Again"),o.proceedToNextStep(n.scanResultBlob)):c.cancelDueToNetworkError(n.errorMessage||"Unexpected API response, cancelling out.",o)}catch(e){console.error("PhotoIDScanProcessor - executeIDScanUseCase error:",e),c.cancelDueToNetworkError(e.message||"Exception while handling API response, cancelling out.",o)}},this.onFaceTecSDKCompletelyDone=function(){null!==c.latestIDScanResult&&(c.success=c.latestIDScanResult.isCompletelyDone),c.success&&s.logMessage("Id Scan Complete"),c.sampleAppControllerReference.onComplete(null,c.latestIDScanResult,200)},this.cancelDueToNetworkError=function(e,o){!1===c.cancelledDueToNetworkError&&(console.error(e),c.cancelledDueToNetworkError=!0,o.cancel())},this.isSuccess=function(){return c.success},this.success=!1,this.sampleAppControllerReference=o,this.latestIDScanResult=null,this.cancelledDueToNetworkError=!1,FaceTecSDK.FaceTecCustomization.setIDScanUploadMessageOverrides("Uploading<br/>Encrypted<br/>ID Scan","Still Uploading...<br/>Slow Connection","Upload Complete","Processing<br/>ID Scan","Uploading<br/>Encrypted<br/>Back of ID","Still Uploading...<br/>Slow Connection","Upload Complete","Processing<br/>Back of ID","Uploading<br/>Your Confirmed Info","Still Uploading...<br/>Slow Connection","Info Saved","Processing"),new FaceTecSDK.FaceTecSession(this,e)}},985:e=>{e.exports=class{constructor(e){this.authRepository=e}async execute(e={}){return await this.authRepository.getFaceTecSessionTokenWithEkycToken(e)}}},995:e=>{e.exports=class{constructor(e){this.authRepository=e}async execute(e={}){return await this.authRepository.getSessionToken(e)}}}},o={},function t(n){var s=o[n];if(void 0!==s)return s.exports;var r=o[n]={exports:{}};return e[n](r,r.exports,t),r.exports}(347);var e,o}));